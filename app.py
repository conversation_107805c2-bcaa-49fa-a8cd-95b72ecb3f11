from flask import Flask, render_template, request, jsonify
import math

app = Flask(__name__)

def calculate_storm_intensity(t, P):
    """
    Calculate storm intensity q using the formula:
    q = 1600(1 + 0.846 lg P) / (t + 7.0)^0.656
    
    Args:
        t: Rainfall duration (minutes)
        P: Design storm return period (years)
    
    Returns:
        q: Storm intensity (l/s·hm²)
    """
    q = (1600 * (1 + 0.846 * math.log10(P))) / ((t + 7.0) ** 0.656)
    return q

def calculate_rainwater_flow_rate(psi, F, t, P):
    """
    Calculate rainwater design flow rate using the formula:
    Q_s = q * Ψ * F
    
    Args:
        psi: Runoff coefficient (dimensionless)
        F: Catchment area (hm²)
        t: Rainfall duration (minutes)
        P: Design storm return period (years)
    
    Returns:
        dict: Contains q (storm intensity) and Q_s (design flow rate)
    """
    q = calculate_storm_intensity(t, P)
    Q_s = q * psi * F
    
    return {
        'storm_intensity': q,
        'design_flow_rate': Q_s
    }

def calculate_design_flow_rate(b, h, I, n):
    """
    Calculate design flow rate using the formula:
    Q = A * v
    
    Where:
    A = b * h (cross-sectional area)
    R = (b*h)/(b+2*h) (hydraulic radius)
    v = (1/n) * R^(2/3) * I^(1/2) (flow velocity)
    
    Args:
        b: Rectangular cross-section width (m)
        h: Rectangular cross-section height (m)
        I: Hydraulic slope (dimensionless)
        n: Roughness coefficient (dimensionless)
    
    Returns:
        dict: Contains A, R, v, and Q
    """
    # Cross-sectional area
    A = b * h
    
    # Hydraulic radius
    R = (b * h) / (b + 2 * h)
    
    # Flow velocity using Manning's equation
    v = (1 / n) * (R ** (2/3)) * (I ** 0.5)
    
    # Design flow rate
    Q = A * v
    
    return {
        'cross_sectional_area': A,
        'hydraulic_radius': R,
        'flow_velocity': v,
        'design_flow_rate': Q
    }

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/calculate_rainwater', methods=['POST'])
def calculate_rainwater():
    try:
        data = request.get_json()
        psi = float(data['psi'])
        F = float(data['F'])
        t = float(data['t'])
        P = float(data['P'])
        
        result = calculate_rainwater_flow_rate(psi, F, t, P)
        
        return jsonify({
            'success': True,
            'storm_intensity': round(result['storm_intensity'], 3),
            'design_flow_rate': round(result['design_flow_rate'], 3)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/calculate_design', methods=['POST'])
def calculate_design():
    try:
        data = request.get_json()
        b = float(data['b'])
        h = float(data['h'])
        I = float(data['I'])
        n = float(data['n'])
        
        result = calculate_design_flow_rate(b, h, I, n)
        
        return jsonify({
            'success': True,
            'cross_sectional_area': round(result['cross_sectional_area'], 4),
            'hydraulic_radius': round(result['hydraulic_radius'], 4),
            'flow_velocity': round(result['flow_velocity'], 4),
            'design_flow_rate': round(result['design_flow_rate'], 4)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

if __name__ == '__main__':
    app.run(debug=True)
