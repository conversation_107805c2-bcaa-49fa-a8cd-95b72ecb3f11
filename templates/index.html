<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hydraulic Engineering Flow Rate Calculators</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            }
        };
    </script>
</head>
<body>
    <div class="header">
        <h1>Hydraulic Engineering Flow Rate Calculators</h1>
    </div>
    
    <div class="container">
        <!-- Part 1: Rainwater Design Flow Rate Calculator -->
        <div class="panel left-panel">
            <h2>Part 1: Rainwater Design Flow Rate Calculator</h2>
            <div class="formula">
                <p><strong>Formula:</strong> $Q_s = q \Psi F$</p>
                <p><strong>Storm Intensity:</strong> $q = \frac{1600(1 + 0.846 \lg P)}{(t + 7.0)^{0.656}}$</p>
            </div>
            
            <form id="rainwater-form">
                <div class="input-group">
                    <label for="psi">Runoff Coefficient ($\Psi$):</label>
                    <input type="number" id="psi" name="psi" step="0.01" min="0" max="1" required>
                    <span class="unit">(dimensionless)</span>
                </div>
                
                <div class="input-group">
                    <label for="F">Catchment Area ($F$):</label>
                    <input type="number" id="F" name="F" step="0.01" min="0" required>
                    <span class="unit">(hm²)</span>
                </div>
                
                <div class="input-group">
                    <label for="t">Rainfall Duration ($t$):</label>
                    <input type="number" id="t" name="t" step="1" min="1" required>
                    <span class="unit">(minutes)</span>
                </div>
                
                <div class="input-group">
                    <label for="P">Design Storm Return Period ($P$):</label>
                    <input type="number" id="P" name="P" step="1" min="1" required>
                    <span class="unit">(years)</span>
                </div>
                
                <button type="submit">Calculate $Q_s$</button>
            </form>
            
            <div id="rainwater-results" class="results">
                <h3>Results:</h3>
                <div id="rainwater-calculations"></div>
            </div>
        </div>
        
        <!-- Part 2: Design Flow Rate Calculator -->
        <div class="panel right-panel">
            <h2>Part 2: Design Flow Rate Calculator</h2>
            <div class="formula">
                <p><strong>Formula:</strong> $Q = A v$</p>
                <p><strong>Where:</strong></p>
                <ul>
                    <li>$A = b \times h$ (Cross-sectional area)</li>
                    <li>$R = \frac{bh}{b+2h}$ (Hydraulic radius)</li>
                    <li>$v = \frac{1}{n} R^{\frac{2}{3}} I^{\frac{1}{2}}$ (Flow velocity)</li>
                </ul>
            </div>
            
            <form id="design-form">
                <div class="input-group">
                    <label for="b">Cross-section Width ($b$):</label>
                    <input type="number" id="b" name="b" step="0.01" min="0" required>
                    <span class="unit">(m)</span>
                </div>
                
                <div class="input-group">
                    <label for="h">Cross-section Height ($h$):</label>
                    <input type="number" id="h" name="h" step="0.01" min="0" required>
                    <span class="unit">(m)</span>
                </div>
                
                <div class="input-group">
                    <label for="I">Hydraulic Slope ($I$):</label>
                    <input type="number" id="I" name="I" step="0.0001" min="0" required>
                    <span class="unit">(dimensionless)</span>
                </div>
                
                <div class="input-group">
                    <label for="n">Roughness Coefficient ($n$):</label>
                    <input type="number" id="n" name="n" step="0.001" min="0" required>
                    <span class="unit">(dimensionless)</span>
                </div>
                
                <button type="submit">Calculate $Q$</button>
            </form>
            
            <div id="design-results" class="results">
                <h3>Results:</h3>
                <div id="design-calculations"></div>
            </div>
        </div>
    </div>
    
    <script>
        // Rainwater calculator
        document.getElementById('rainwater-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {
                psi: formData.get('psi'),
                F: formData.get('F'),
                t: formData.get('t'),
                P: formData.get('P')
            };
            
            try {
                const response = await fetch('/calculate_rainwater', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('rainwater-calculations').innerHTML = `
                        <p><strong>Storm Intensity ($q$):</strong> ${result.storm_intensity} l/s·hm²</p>
                        <p><strong>Rainwater Design Flow Rate ($Q_s$):</strong> ${result.design_flow_rate} l/s</p>
                    `;
                    MathJax.typesetPromise([document.getElementById('rainwater-calculations')]);
                } else {
                    document.getElementById('rainwater-calculations').innerHTML = `
                        <p style="color: red;">Error: ${result.error}</p>
                    `;
                }
            } catch (error) {
                document.getElementById('rainwater-calculations').innerHTML = `
                    <p style="color: red;">Error: ${error.message}</p>
                `;
            }
        });
        
        // Design flow rate calculator
        document.getElementById('design-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {
                b: formData.get('b'),
                h: formData.get('h'),
                I: formData.get('I'),
                n: formData.get('n')
            };
            
            try {
                const response = await fetch('/calculate_design', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('design-calculations').innerHTML = `
                        <p><strong>Cross-sectional Area ($A$):</strong> ${result.cross_sectional_area} m²</p>
                        <p><strong>Hydraulic Radius ($R$):</strong> ${result.hydraulic_radius} m</p>
                        <p><strong>Flow Velocity ($v$):</strong> ${result.flow_velocity} m/s</p>
                        <p><strong>Design Flow Rate ($Q$):</strong> ${result.design_flow_rate} m³/s</p>
                    `;
                    MathJax.typesetPromise([document.getElementById('design-calculations')]);
                } else {
                    document.getElementById('design-calculations').innerHTML = `
                        <p style="color: red;">Error: ${result.error}</p>
                    `;
                }
            } catch (error) {
                document.getElementById('design-calculations').innerHTML = `
                    <p style="color: red;">Error: ${error.message}</p>
                `;
            }
        });
    </script>
</body>
</html>
