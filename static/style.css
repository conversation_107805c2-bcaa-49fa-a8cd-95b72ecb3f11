/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 2rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 300;
    margin: 0;
}

/* Container and layout */
.container {
    display: flex;
    max-width: 1400px;
    margin: 2rem auto;
    gap: 2rem;
    padding: 0 1rem;
}

.panel {
    flex: 1;
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.panel:hover {
    transform: translateY(-5px);
}

.panel h2 {
    color: #4a5568;
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
    border-bottom: 3px solid #667eea;
    padding-bottom: 0.5rem;
}

/* Formula display */
.formula {
    background: #f8f9fa;
    border-left: 4px solid #667eea;
    padding: 1rem;
    margin-bottom: 2rem;
    border-radius: 0 5px 5px 0;
}

.formula p {
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.formula ul {
    margin-left: 1.5rem;
    margin-top: 0.5rem;
}

.formula li {
    margin-bottom: 0.3rem;
}

/* Form styles */
form {
    margin-bottom: 2rem;
}

.input-group {
    margin-bottom: 1.5rem;
}

.input-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #4a5568;
    font-size: 1.1rem;
}

.input-group input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.unit {
    display: inline-block;
    margin-top: 0.25rem;
    font-size: 0.9rem;
    color: #718096;
    font-style: italic;
}

/* Button styles */
button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 5px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

button:active {
    transform: translateY(0);
}

/* Results section */
.results {
    background: #f7fafc;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
}

.results h3 {
    color: #4a5568;
    margin-bottom: 1rem;
    font-size: 1.4rem;
}

.results p {
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    padding: 0.5rem;
    background: white;
    border-radius: 4px;
    border-left: 3px solid #48bb78;
}

.results p:last-child {
    margin-bottom: 0;
}

.results p strong {
    color: #2d3748;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        margin: 1rem auto;
        gap: 1rem;
    }
    
    .panel {
        padding: 1.5rem;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .panel h2 {
        font-size: 1.5rem;
    }
}

/* Error message styling */
.results p[style*="color: red"] {
    border-left-color: #e53e3e;
    background: #fed7d7;
    color: #c53030;
}

/* Loading state */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Animation for results */
.results {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
