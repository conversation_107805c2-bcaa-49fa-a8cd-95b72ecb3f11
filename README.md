# Hydraulic Engineering Flow Rate Calculators

A Python web application for calculating two types of flow rates used in hydraulic engineering projects.

## Features

### Part 1: Rainwater Design Flow Rate Calculator (Qs)
Calculates rainwater design flow rate using the formula:
- **Qs = q × Ψ × F**
- Automatically calculates storm intensity: **q = 1600(1 + 0.846 lg P) / (t + 7.0)^0.656**

**Input Parameters:**
- Ψ: Runoff coefficient (dimensionless)
- F: Catchment area (hm²)
- t: Rainfall duration (minutes)
- P: Design storm return period (years)

**Output:**
- Storm intensity (q) in l/s·hm²
- Rainwater design flow rate (Qs) in l/s

### Part 2: Design Flow Rate Calculator (Q)
Calculates design flow rate using the formula:
- **Q = A × v**

**Input Parameters:**
- b: Rectangular cross-section width (m)
- h: Rectangular cross-section height (m)
- I: Hydraulic slope (dimensionless)
- n: Roughness coefficient (dimensionless)

**Automatic Calculations:**
- Cross-sectional area: A = b × h (m²)
- Hydraulic radius: R = (b×h)/(b+2h) (m)
- Flow velocity: v = (1/n) × R^(2/3) × I^(1/2) (m/s)

**Output:**
- Cross-sectional area (A) in m²
- Hydraulic radius (R) in m
- Flow velocity (v) in m/s
- Design flow rate (Q) in m³/s

## Installation and Setup

1. **Install Dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the Application:**
   ```bash
   python app.py
   ```

3. **Access the Application:**
   Open your web browser and navigate to: `http://127.0.0.1:5000`

## Usage

1. **Part 1 (Left Panel):** Enter the runoff coefficient, catchment area, rainfall duration, and design storm return period. Click "Calculate Qs" to get the results.

2. **Part 2 (Right Panel):** Enter the cross-section dimensions, hydraulic slope, and roughness coefficient. Click "Calculate Q" to get the results.

3. **Results:** Both calculators display intermediate calculations for transparency and the final flow rate results.

## Technical Details

- **Backend:** Flask (Python)
- **Frontend:** HTML, CSS, JavaScript
- **Mathematical Rendering:** MathJax for formula display
- **Responsive Design:** Works on desktop and mobile devices

## File Structure

```
paishui/
├── app.py                 # Main Flask application
├── requirements.txt       # Python dependencies
├── templates/
│   └── index.html        # HTML template
├── static/
│   └── style.css         # CSS styling
└── README.md             # This file
```

## Mathematical Formulas

The application implements the following hydraulic engineering formulas:

1. **Storm Intensity Calculation:**
   ```
   q = 1600(1 + 0.846 lg P) / (t + 7.0)^0.656
   ```

2. **Rainwater Design Flow Rate:**
   ```
   Qs = q × Ψ × F
   ```

3. **Manning's Equation for Flow Velocity:**
   ```
   v = (1/n) × R^(2/3) × I^(1/2)
   ```

4. **Design Flow Rate:**
   ```
   Q = A × v
   ```

## Notes

- All calculations are performed server-side for accuracy
- Results are rounded to appropriate decimal places
- Input validation ensures positive values where required
- Error handling provides user-friendly feedback
